<?php

use App\Models\User;
use App\Services\CachingService;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\FaqController;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\FeesController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\AddonController;
use App\Http\Controllers\LeaveController;
use App\Http\Controllers\ShiftController;
use App\Http\Controllers\StaffController;
use App\Http\Controllers\CreditController;
use App\Http\Controllers\LessonController;
use App\Http\Controllers\MediumController;
use App\Http\Controllers\RewardController;
use App\Http\Controllers\SchoolController;
use App\Http\Controllers\SliderController;
use App\Http\Controllers\StreamController;
use App\Http\Controllers\BookingController;
use App\Http\Controllers\ExpenseController;
use App\Http\Controllers\GalleryController;
use App\Http\Controllers\HolidayController;
use App\Http\Controllers\PackageController;
use App\Http\Controllers\PayrollController;
use App\Http\Controllers\SectionController;
use App\Http\Controllers\StudentController;
use App\Http\Controllers\SubjectController;
use App\Http\Controllers\TeacherController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\FeesTypeController;
use App\Http\Controllers\GuardianController;
use App\Http\Controllers\GuidanceController;
use App\Http\Controllers\ItemCodeController;
use App\Http\Controllers\LanguageController;
use App\Http\Controllers\SemesterController;
use App\Http\Controllers\SupplierController;
use App\Http\Controllers\AllowanceController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\DebitNoteController;
use App\Http\Controllers\DeductionController;
use App\Http\Controllers\Exam\ExamController;
use App\Http\Controllers\InstallerController;
use App\Http\Controllers\StatementController;
use App\Http\Controllers\TimetableController;
use App\Http\Controllers\UserGroupController;
use App\Http\Controllers\AssignmentController;
use App\Http\Controllers\AttendanceController;
use App\Http\Controllers\ClassGroupController;
use App\Http\Controllers\CommissionController;
use App\Http\Controllers\Exam\GradeController;
use App\Http\Controllers\FormFieldsController;
use App\Http\Controllers\OnlineExamController;
use App\Http\Controllers\RefundNoteController;
use App\Http\Controllers\ClassSchoolController;
use App\Http\Controllers\LeaveMasterController;
use App\Http\Controllers\LessonTopicController;
use App\Http\Controllers\SelfBillingController;
use App\Http\Controllers\SessionYearController;
use App\Http\Controllers\StudentFeesController;
use App\Http\Controllers\WebSettingsController;
use App\Http\Controllers\AnnouncementController;
use App\Http\Controllers\ClassSectionController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\RewardRedeemController;
use App\Http\Controllers\SchoolBranchController;
use App\Http\Controllers\SubscriptionController;
use App\Http\Controllers\SystemUpdateController;
use App\Http\Controllers\BookingSettingController;
use App\Http\Controllers\PayrollSettingController;
use App\Http\Controllers\PromoteStudentController;
use App\Http\Controllers\RewardCategoryController;
use App\Http\Controllers\SchoolSettingsController;
use App\Http\Controllers\SubjectPackageController;
use App\Http\Controllers\SystemSettingsController;
use App\Http\Controllers\ExpenseCategoryController;
use App\Http\Controllers\FinancialReportController;
use App\Http\Controllers\StudentFeeTypesController;
use App\Http\Controllers\SubjectStudentsController;
use App\Http\Controllers\ManageCommissionController;
use App\Http\Controllers\TeacherAttendanceController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\Exam\ExamTimetableController;
use App\Http\Controllers\OnlineExamQuestionController;
use App\Http\Controllers\CertificateTemplateController;
use App\Http\Controllers\SubscriptionWebhookController;
use App\Http\Controllers\SubscriptionBillPaymentController;
use App\Http\Controllers\ClassReportController;
use App\Http\Controllers\DepositTypeController;
use App\Http\Controllers\StudentDepositController;
use App\Http\Controllers\RecurringFeesController;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Auth::routes();
Route::get('/', [AuthController::class, 'login']);
Route::get('/', [Controller::class, 'index']);


Route::get('class-report', [ClassReportController::class, 'index'])->name('class-report.index');
Route::get('class-report/{class_section_id}', [ClassReportController::class, 'show'])->name('class-report.show');

Route::post('schools/registration', [SchoolController::class, 'registration']);
Route::get('school/registration', [SchoolController::class, 'registrationForm']);
Route::post('contact', [Controller::class, 'contact']);
Route::get('subscription/cron-job', [Controller::class, 'cron_job']);
Route::get('set-language/{lang}', [LanguageController::class, 'set_language']);

Route::post('password/reset', [ResetPasswordController::class, 'reset'])->name('password.update');

Route::get('/check-duplicate-student', [StudentController::class, 'checkDuplicate'])->name('check-duplicate-student');
Route::get('students/admission-form', [StudentController::class, 'admissionForm'])->name('admission.form');
Route::get('admission-form-page',[Controller::class,'admissionFormPage']);
Route::get('admission-form/{school_code}', [Controller::class, 'admissionFormPublic']);
Route::get('terms-condition/{schoolId}', [Controller::class, 'getTermsCondition']);
Route::post('admission', [Controller::class, 'admission'])->name('admission.page');
Route::get('attendance-schoolsub/{school_code}', [Controller::class, 'attendanceSchoolSub']);
Route::get('attendancetracker/{school_id}/{subject_id}/{teacher_id}/{role}', [Controller::class, 'attendanceTracker']);
Route::get('recurringclonjob', [Controller::class, 'recurringCronJob']);
Route::get('packageCronJob', [Controller::class, 'packageCronJob']);
Route::post('take-attendance', [Controller::class, 'takeAttendance'])->name('attendance.take');
Route::get('/student-fees-overdue/cron-job', [Controller::class, 'overdueCronjob']);
Route::get('gallery/get-student-tag', [GalleryController::class, 'getStudentTags'])->name('gallery.get-student-tag');
Route::get('/login_to/{id}', [SchoolController::class, 'loginToSchool'])->name('school.login_to');
Route::get('attendance-tracker/rfid',[Controller::class,'attendanceRFIDTimetable'])->name('attendance-tracker.rfid');
Route::post('attendance-tracker/rfid/take',[Controller::class,'takeAttendanceRFIDTimetable'])->name('attendance-tracker.rfid-take');
Route::post('attendance-tracker/check-teacher',[Controller::class,'checkTeacherOrStudent'])->name('attendance-tracker.check-teacher');
// Route::get('reward-schoolsub', [Controller::class, 'rewardSchoolSub'])->name('rewardpoint.view');
Route::get('rewardpoint/', [Controller::class, 'rewardPoint']);

Route::post('grant-rewardpoint', [Controller::class, 'grantRewardPoint'])->name('rewardpoint.grant');


Route::domain('{subdomain}.' . parse_url(Request::getSchemeAndHttpHost(), PHP_URL_HOST))->group(function () {
    Route::get('/', [Controller::class, 'index']);
});

Route::domain('{subdomain}' . '.schola.one')->group(function () {
    Route::get('/', [Controller::class, 'index']);
});

Route::group(['prefix' => 'school'], static function () {
    Route::get('about-us', [Controller::class, 'about_us']);
    Route::get('contact-us', [Controller::class, 'contact_us']);
    Route::post('contact-us', [Controller::class, 'contact_form']);
    Route::get('photos', [Controller::class, 'photo']);
    Route::get('photos/{id}', [Controller::class, 'photo_file']);
    Route::get('videos', [Controller::class, 'video']);
    Route::get('videos/{id}', [Controller::class, 'video_file']);
    Route::get('terms-conditions', [Controller::class, 'terms_conditions']);
    Route::get('privacy-policy', [Controller::class, 'privacy_policy']);
    Route::get('refund-cancellation-policy', [Controller::class, 'refund_cancellation']);
    Route::get('online-admission',[Controller::class, 'admission'])->name('online-admission.index');
    Route::post('online-admission',[Controller::class, 'registerStudent'])->name('online-admission.store');
});


Route::group(['prefix' => 'install'], static function () {
    Route::get('purchase-code', [InstallerController::class, 'purchaseCodeIndex'])->name('install.purchase-code.index');
    Route::post('purchase-code', [InstallerController::class, 'checkPurchaseCode'])->name('install.purchase-code.post');
});
Route::group(['middleware' => ['Role', 'auth', 'checkSchoolStatus', 'status']], static function () {
    Route::group(['middleware' => 'language'], static function () {
        // Deposit Types Management
        Route::resource('deposit-types', DepositTypeController::class);
        Route::post('deposit-types/{id}/restore', [DepositTypeController::class, 'restore'])
            ->name('deposit-types.restore');

        Route::resource('student-deposits', StudentDepositController::class);
        Route::get('student-deposits-export', [StudentDepositController::class, 'export'])
            ->name('student-deposits.export');
        Route::post('student-deposits/{studentDeposit}/refund', [StudentDepositController::class, 'processRefund'])
            ->name('student-deposits.refund');
        Route::post('student-deposits/{id}/restore', [StudentDepositController::class, 'restore'])
            ->name('student-deposits.restore');

        /*** Dashboard ***/
        Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');
        Route::get('home', [DashboardController::class, 'index'])->name('home');
        Route::post('update-mail-notification',[DashboardController::class,'updateMailNotification'])->name('update-mail-notification');

        /*** School Branch ***/
        Route::get('branch', [SchoolBranchController::class, 'index'])->name('branch.index');
        Route::get('branch/show', [SchoolBranchController::class, 'show'])->name('branch.show');
        // Route::get('school/login_as/{id}', [SchoolBranchController::class, 'loginAsSchool'])->name('school.login_as');
        Route::get('branch/list', [SchoolBranchController::class, 'branchList'])->name('branch.list');


        /*** Auth ***/
        Route::group(['prefix' => 'auth'], static function () {
            Route::get('logout', [AuthController::class, 'logout'])->name('auth.logout');
            Route::get('check-password', [AuthController::class, 'checkPassword'])->name('auth.check-password');
            Route::get('change-password', [AuthController::class, 'changePasswordIndex'])->name('auth.change-password.index');
            Route::post('change-password', [AuthController::class, 'changePasswordStore'])->name('auth.change-password.update');
            Route::get('profile', [AuthController::class, 'profileEdit'])->name('auth.profile.edit');
            Route::put('profile', [AuthController::class, 'profileUpdate'])->name('auth.profile.update');
        });

        /*** Role & Staff management ***/
        Route::get('staff/support', [StaffController::class, 'support']);
        Route::get("/roles-list", [RoleController::class, 'list'])->name('roles.list');
        Route::resource('roles', RoleController::class);

        Route::group(['prefix' => 'staff'], static function () {
            Route::get('id-card', [StaffController::class, 'staff_id_card'])->name('staff.id-card');
            Route::get('id-card-list', [StaffController::class, 'staff_id_card_list'])->name('staff.show.all');
            Route::post('generate-id-card', [StaffController::class, 'generate_staff_id_card']);
            Route::get('download-dummy-file', [StaffController::class, 'downloadSampleFile'])->name('staff.bulk-data-sample');
            Route::get("create-bulk-upload", [StaffController::class, 'bulkUploadIndex'])->name('staff.create-bulk-upload');
            Route::post("store-bulk-upload", [StaffController::class, 'storeBulkUpload'])->name('staff.store-bulk-upload');
            Route::get('payroll-structure/{id}', [StaffController::class, 'viewSalaryStructure'])->name('staff.payroll-structure');

            Route::delete('payroll-setting/{id}', [StaffController::class, 'deletePayrollSetting']);
            Route::put('payroll-setting/{id}', [StaffController::class, 'updatePayrollSetting']);
        });

        Route::resource('staff', StaffController::class);
        Route::put("staff/{id}/change-status", [StaffController::class, 'restore'])->name('staff.restore');
        Route::delete("staff/{id}/deleted", [StaffController::class, 'trash'])->name('staff.trash');
        Route::post("staff/change-status-bulk", [StaffController::class, 'changeStatusBulk'])->name('staff.change-status-bulk');

        /*** Medium ***/
        Route::group(['prefix' => 'mediums'], static function () {
            Route::put("/{id}/restore", [MediumController::class, 'restore'])->name('mediums.restore');
            Route::delete("/{id}/deleted", [MediumController::class, 'trash'])->name('mediums.trash');
        });
        Route::resource('mediums', MediumController::class);


        /*** Section ***/
        Route::group(['prefix' => 'section'], static function () {
            Route::put("/{id}/restore", [SectionController::class, 'restore'])->name('section.restore');
            Route::delete("/{id}/deleted", [SectionController::class, 'trash'])->name('section.trash');
        });
        Route::resource('section', SectionController::class);


        /*** Subject ***/
        Route::group(['prefix' => 'subject'], static function () {
            Route::put("/{id}/restore", [SubjectController::class, 'restore'])->name('subject.restore');
            Route::delete("/{id}/deleted", [SubjectController::class, 'trash'])->name('subject.trash');
        });
        Route::resource('subject', SubjectController::class);
        Route::get("subject-students", [StudentFeesController::class, 'getSubjectStudent'])->name('scan-attendance.subject-students');

        // Subject Students
        Route::get('/subjects-students', [SubjectStudentsController::class, 'index'])->name('subject-students.index');
        Route::post('/subjects-students-store', [SubjectStudentsController::class, 'store'])->name('subject-students.store');
        Route::get('/subjects-students-show/{id?}', [SubjectStudentsController::class, 'show'])->name('subject-students.show');

        Route::get('/subjects-students/{id}/edit', [SubjectStudentsController::class, 'edit'])->name('subject-students.edit');
        Route::delete('/subjects-students/{id}/update', [SubjectStudentsController::class, 'remove'])->name('subject-students.remove');
        Route::delete('/subjects-students/{id}', [SubjectStudentsController::class, 'delete'])->name('subject-students.delete');

        // Subject Package
        Route::group(['prefix' => 'subject-package'], static function () {
            Route::get('/', [SubjectPackageController::class, 'packageIndex'])->name('subject-package.index');
            Route::get('/show', [SubjectPackageController::class, 'show'])->name('subject-package.show');

            Route::get('/new', [SubjectPackageController::class, 'newPackageIndex'])->name('subject-package.new');
            Route::post('/store', [SubjectPackageController::class, 'store'])->name('subject-package.store');
            Route::get('/{id}/edit', [SubjectPackageController::class, 'edit'])->name('subject-package.edit');
            Route::post('/{id}/update', [SubjectPackageController::class, 'update'])->name('subject-package.update');
            Route::delete('/{id}', [SubjectPackageController::class, 'destroy'])->name('subject-package.destroy');

            Route::get('/purchase', [SubjectPackageController::class, 'purchaseIndex'])->name('subject-package.purchase');
            Route::post('/purchase/store', [SubjectPackageController::class, 'purchaseStore'])->name('subject-package.purchaseStore');
            Route::get('/purchase/show', [SubjectPackageController::class, 'purchaseShow'])->name('subject-package.purchaseShow');
            Route::delete('/purchase/{id}', [SubjectPackageController::class, 'purchaseDestroy'])->name('subject-package.purchaseDestroy');
            Route::get('subject-package/get-students', [SubjectPackageController::class, 'getStudents'])->name('subject-package.getStudents');

            Route::get('/usage', [SubjectPackageController::class, 'usageIndex'])->name('subject-package.usage');
            Route::get('/usage/show', [SubjectPackageController::class, 'usageShow'])->name('subject-package.usageShow');
            Route::delete('/usage/{id}', [SubjectPackageController::class, 'usageDestroy'])->name('subject-package.usageDestroy');
            
            Route::get('/history/{student_id?}', [SubjectPackageController::class, 'historyIndex'])->name('subject-package.history');
            Route::get('/history/show/{student_id?}', [SubjectPackageController::class, 'historyShow'])->name('subject-package.historyShow');
            Route::get('/history/{id}/edit', [SubjectPackageController::class, 'historyEdit'])->name('subject-package.historyEdit');
            Route::post('/purchase/{id}/update', [SubjectPackageController::class, 'historyUpdate'])->name('subject-package.historyUpdate');

        });

        /*** Class ***/
        Route::group(['prefix' => 'class'], static function () {
            Route::put("/{id}/restore", [ClassSchoolController::class, 'restore'])->name('class.restore');
            Route::delete("/{id}/deleted", [ClassSchoolController::class, 'trash'])->name('class.trash');
            Route::get('/subject', [ClassSchoolController::class, 'classSubjectIndex'])->name('class.subject.index');
            Route::get('/subject/{id}/edit', [ClassSchoolController::class, 'classSubjectEdit'])->name('class.subject.edit');
            Route::put('/subject/{id}/edit', [ClassSchoolController::class, 'classSubjectUpdate'])->name('class.subject.update');
            Route::get('/subject/list', [ClassSchoolController::class, 'classSubjectList'])->name('class.subject.list');
            Route::delete('/subject/{class_subject_id}', [ClassSchoolController::class, 'deleteClassSubject'])->name('class.subject.destroy');
            Route::delete('/subject-group/{group_id}', [ClassSchoolController::class, 'deleteClassSubjectGroup'])->name('class.subject-group.destroy');

            Route::get('/attendance/{id?}', [ClassSchoolController::class, 'classAttendance']);
        });
        Route::resource('class', ClassSchoolController::class);

        /*** Class Section ***/
        Route::group(['prefix' => 'class-section'], static function () {
            Route::delete('class-teacher/remove/{id}/{class_section_id}', [ClassSectionController::class, 'removeClassTeacher']);
            Route::delete('subject-teacher/remove/{class_section_id}/{teacher_id}/{subject_id}', [ClassSectionController::class, 'removeSubjectTeacher']);
            Route::put("/{id}/restore", [ClassSectionController::class, 'restore'])->name('class-section.restore');
            Route::delete("/{id}/trash", [ClassSectionController::class, 'trash'])->name('class-section.trash');
        });
        Route::resource('class-section', ClassSectionController::class);

        /*** Teachers ***/
        Route::group(['prefix' => 'teachers'], static function () {
            Route::put("/{id}/restore", [TeacherController::class, 'restore'])->name('teachers.restore');
            Route::delete("/{id}/deleted", [TeacherController::class, 'trash'])->name('teachers.trash');
            Route::put("change/status/{id}", [TeacherController::class, 'changeStatus'])->name('teachers.change-status');
            Route::post("/change-status-bulk", [TeacherController::class, 'changeStatusBulk'])->name('staff.change-status-bulk');
            Route::get('download-dummy-file', [TeacherController::class, 'downloadSampleFile'])->name('teachers.bulk-data-sample');
            Route::get('/{id}/email', [TeacherController::class, 'sendWelcomeEmail'])->name('teachers.email');
            Route::get("create-bulk-upload", [TeacherController::class, 'bulkUploadIndex'])->name('teachers.create-bulk-upload');
            Route::post("store-bulk-upload", [TeacherController::class, 'storeBulkUpload'])->name('teachers.store-bulk-upload');
        });
        Route::resource('teachers', TeacherController::class);


        /*** Parents ***/
        Route::get('/guardian/search', [GuardianController::class, 'search']);
        Route::get('/guardian/email/{id}', [GuardianController::class, 'sendGuardianEmail'])->name('guardian.email');
        Route::post('/guardian/password/{id}', [GuardianController::class, 'resetPassword'])->name('guardian.resetPassword');
        Route::resource('guardian', GuardianController::class);

        /*** Students ***/
        Route::group(['prefix' => 'students'], static function () {
            Route::get('create-bulk', [StudentController::class, 'createBulkData'])->name('students.create-bulk-data');
            Route::post('store-bulk', [StudentController::class, 'storeBulkData'])->name('students.store-bulk-data');

            // Update bulk profile student & guardian
            Route::get('update-profile', [StudentController::class, 'update_profile'])->name('students.upload-profile');
            Route::get('list/{id?}', [StudentController::class, 'list'])->name('students.list');
            Route::post('update-profile', [StudentController::class, 'store_update_profile'])->name('students.update-profile');


            Route::get('download-file', [StudentController::class, 'downloadSampleFile'])->name('student.bulk-data-sample');
            Route::delete('change-status/{id}', [StudentController::class, 'changeStatus'])->name('student.change-status');
            /*** Reset Password ***/
            Route::get('reset-password', [StudentController::class, 'resetPasswordIndex'])->name('students.reset-password.index');
            Route::post('reset-password', [StudentController::class, 'resetPasswordUpdate'])->name('student.reset-password.update');
            Route::get('reset-password-list', [StudentController::class, 'resetPasswordShow'])->name('student.reset-password.show');

            /*** Roll Number ***/
            Route::get('roll-number', [StudentController::class, 'rollNumberIndex'])->name('students.roll-number.index');
            Route::post('roll-number', [StudentController::class, 'rollNumberUpdate'])->name('students.roll-number.update');
            Route::get('roll-number-list', [StudentController::class, 'rollNumberShow'])->name('students.roll-number.show');
            Route::post("change-status-bulk", [StudentController::class, 'changeStatusBulk'])->name('students.change-status-bulk');

            Route::delete("/{id}/deleted", [StudentController::class, 'trash'])->name('student.trash');

            Route::get('generate-id-card', [StudentController::class, 'generate_id_card_index'])->name('students.generate-id-card-index');
            Route::post('generate-id-card', [StudentController::class, 'generate_id_card'])->name('students.generate-id-card');
            Route::get('id-card-settings', [StudentController::class, 'id_card_index'])->name('student.id-card-settings');
            Route::post('id-card-settings', [StudentController::class, 'id_card_store']);
            Route::get('id-card/remove/{type}', [StudentController::class, 'remove_image_from_id_card']);

            Route::get('/students/student_subject', [StudentController::class, 'studentSubjectIndex'])->name('students.student_subject-index');
            Route::get('/students/student_subject/show', [StudentController::class, 'showStudentSubject'])->name('students.student_subject.show');
            Route::get('/students/student_subject/edit/{id}', [StudentController::class, 'editStudentSubject'])->name('students.student_subject.edit');
            Route::put('/students/student_subject/update', [StudentController::class, 'updateStudentSubject'])->name('students.student_subject.update');

            Route::get('/students/student-progress', [StudentController::class, 'studentProgress'])->name('students.student-progress');
            Route::get('/students/student-progress/show', [StudentController::class, 'showStudentProgress'])->name('students.student-progress.show');
            Route::post('/students/student-progress/store', [StudentController::class, 'studentProgressStore'])->name('students.student-progress.store');
            Route::put('/students/student-progress/update/{id}', [StudentController::class, 'updateStudentProgress'])->name('students.student-progress.update');
            Route::delete('/students/student-progress/delete/{id}', [StudentController::class, 'studentListDestroy'])->name('students.student-progress-delete');
            Route::post('/students/progress/verify/{id}/{status}', [StudentController::class, 'verifyProgress'])->name('student.progress.verify');
            Route::get('/students/student-progress-class-section', [StudentController::class, 'getStudentsProgressClassSection'])->name('students.student-progress-class-section');
            Route::get('admission-form', [StudentController::class, 'admissionForm'])->name('admission.form');
            
            Route::post('validate-taxpayer-tin', [StudentController::class, 'validateTaxPayerTin'])->name('students.validate-tin');
        });
        Route::resource('students', StudentController::class);

        Route::get('id-card-settings', [SchoolSettingsController::class, 'id_card_index'])->name('id-card-settings');
        Route::post('id-card-settings', [SchoolSettingsController::class, 'id_card_store']);

        Route::get('manage-commission', [ManageCommissionController::class, 'scan'])->name("managecommission.scan");
        Route::get('manage-commission/show', [ManageCommissionController::class, 'showAttendance'])->name('managecommission.scan.show');
        Route::put('manage-commission/update', [ManageCommissionController::class, 'updateAttendance'])->name('managecommission.update');
        Route::put('/manage-commission/updateCommission/{id}', [ManageCommissionController::class, 'updateCommission'])->name('managecommission.updateCommission');

        Route::get('manage-commission/per-month', [ManageCommissionController::class, 'perMonthIndex'])->name('managecommission.per-month.index');
        Route::get('manage-commission/per-month/show', [ManageCommissionController::class, 'perMonthShow'])->name('managecommission.per-month.show');
        Route::post('manage-commission/per-month/store', [ManageCommissionController::class, 'perMonthStore'])->name('managecommission.per-month.store');
        Route::put('manage-commission/per-month/update/{id}', [ManageCommissionController::class, 'perMonthUpdate'])->name('managecommission.per-month.update');
        Route::delete('manage-commission/per-month/delete/{id}', [ManageCommissionController::class, 'perMonthDelete'])->name('managecommission.per-month.delete');



        /*** Timetable ***/
        Route::group(['prefix' => 'timetable'], static function () {
            Route::put('/settings', [TimetableController::class, 'updateTimetableSettings'])->name('timetable.settings');
            Route::group(['prefix' => '/teacher'], static function () {
                Route::get('/', [TimetableController::class, 'teacherIndex'])->name('timetable.teacher.index');
                Route::get('/list', [TimetableController::class, 'teacherList'])->name('timetable.teacher.list');
                Route::get('/show/{teacher_id}', [TimetableController::class, 'teacherShow'])->name('timetable.teacher.show');
            });
            Route::delete('/delete/{id}', [TimetableController::class, 'deleteClassTimetable']);
            Route::get('/timetable_configurations', [TimetableController::class, 'timetable_configurations'])->name('timetable.timetable_configurations');
            Route::post('/timetable_configurations/store', [TimetableController::class, 'timetable_configurations_store'])->name('timetable.timetable_configurations.store');
            Route::get('/timetable_configurations/show', [TimetableController::class, 'timetable_configurations_show'])->name('timetable.timetable_configurations.show');
            Route::put('/timetable_configurations/update/{id}', [TimetableController::class, 'timetable_configurations_update'])->name('timetable.timetable_configurations.update');
            Route::delete('/timetable_configurations/delete/{id}', [TimetableController::class, 'timetable_configurations_delete'])->name('timetable.timetable_configurations.delete');
            Route::delete('/timetable_configurations/destroy/{id}', [TimetableController::class, 'timetable_configurations_destroy'])->name('timetable.timetable_configurations.destroy');
            Route::put('/timetable_configurations/restore/{id}', [TimetableController::class, 'timetable_configurations_restore'])->name('timetable.timetable_configurations.restore');
        });
        Route::resource('timetable', TimetableController::class);

        /*** Attendance ***/
        // TODO : Improve this
        Route::group(['prefix' => 'attendance'], static function () {
            Route::get('view-attendance', [AttendanceController::class, 'view'])->name("attendance.view");
            Route::get('student-attendance-list', [AttendanceController::class, 'attendance_show'])->name('attendance.list.show');
            Route::get('getAttendanceData', [AttendanceController::class, 'getAttendanceData']);
            Route::get('getAttData', [AttendanceController::class, 'getAttData']);
            Route::get('scan-attendance', [AttendanceController::class, 'scan'])->name("attendance.scan");
            Route::get('subject-attendance', [AttendanceController::class, 'SubjectAttendanceindex'])->name("attendance.subject");
            Route::get('subject-attendance/show', [AttendanceController::class, 'subjectattendanceshow'])->name("attendance_subject.show");
            Route::post('subject-attendance/store', [AttendanceController::class, 'SubjectAttendancestore'])->name('SubjectAttendance.store');
            Route::get('get-filtered-data', [AttendanceController::class, 'getFilteredData'])
            ->name('attendance.get-filtered-data');

            //teacher attendance
            Route::get('scan-teacher-attendance', [TeacherAttendanceController::class, 'scan'])->name("attendance.teacher_scan");
            Route::get('/attendance/teacher/show', [TeacherAttendanceController::class, 'showAttendance'])->name('attendance.teacher.show');
            Route::put('attendance/updateTeacherAttendance', [TeacherAttendanceController::class, 'updateStudentAttendance'])->name('attendance.updateTeacherAttendance');
            Route::get('daily-teacher-attendance-report', [TeacherAttendanceController::class, 'dailyAttendanceReportIndex'])->name("attendance.daily-teacher-attendance-report");
            Route::post('/attendance/teacher', [TeacherAttendanceController::class, 'storeAddStudent'])->name('attendance.teacher-store');
            Route::delete("/{id}/deleted", [TeacherAttendanceController::class, 'destroy'])->name('attendance.teacher.destroy');
            Route::get('daily-teacher-report-list', [TeacherAttendanceController::class, 'dailyAttendanceReportList'])->name("attendance.teacher-attendance-report-list");

            // Route::get('scan-attendance-list', [AttendanceController::class, 'showAttendanceScanList'])->name('attendance.scan.list');
            // web.php
            Route::get('/attendance/scan/show', [AttendanceController::class, 'showAttendance'])->name('attendance.scan.show');
            Route::put('attendance/updateStudentAttendance', [AttendanceController::class, 'updateStudentAttendance'])->name('attendance.updateStudentAttendance');
            Route::get('daily-attendance-report', [AttendanceController::class, 'dailyAttendanceReportIndex'])->name("attendance.daily-attendance-report");
            Route::get('attendance/class-subjects', [AttendanceController::class, 'attendanceClassSubjects'])->name("attendance.class-subjects");
            Route::get('daily-attendance-report-list', [AttendanceController::class, 'dailyAttendanceReportList'])->name("attendance.daily-attendance-report-list");
            Route::get('summary-attendance-report', [AttendanceController::class, 'summaryAttendanceData'])->name("attendance.summary-attendance-report");
            Route::get('summary-attendance-report-list', [AttendanceController::class, 'summaryAttendanceDataList'])->name("attendance.summary-attendance-report-list");
            Route::get('get-students-by-status', [AttendanceController::class, 'getStudentsByStatus'])->name("attendance.get-students-by-status");
            Route::get('get-detailed-attendance', [AttendanceController::class, 'getDetailedAttendance'])->name("attendance.get-detailed-attendance");
        });

        Route::get('student-attendance-report', [AttendanceController::class, 'studentAttendanceReport'])->name('attendance.student-attendance-report');
        Route::get('/attendance/student-attendance-report/show', [AttendanceController::class, 'showStudentAttendanceReport'])->name('attendance.student-attendance-report.show');
        Route::get('teacher-attendance-report', [AttendanceController::class, 'teacherAttendanceReport'])->name('attendance.teacher-attendance-report');
        Route::get('/attendance/teacher-attendance-report/show', [AttendanceController::class, 'showTeacherAttendanceReport'])->name('attendance.teacher-attendance-report.show');
        Route::post('/attendance-summary', [AttendanceController::class, 'attendanceSummary']);
        Route::get('/attendance-load-student', [AttendanceController::class, 'loadStudentSubjectAttendance'])->name('attendance.load-students');
        Route::post('/attendance/teacher-store', [AttendanceController::class, 'storeAddStudent'])->name('attendance.student-store');

        Route::resource('attendance', AttendanceController::class);

        /*** Lesson ***/
        Route::group(['prefix' => 'lesson'], static function () {
            Route::get('/search', [LessonController::class, 'search'])->name('lesson.search');
            Route::put("/{id}/restore", [LessonController::class, 'restore'])->name('lesson.restore');
            Route::delete("/{id}/deleted", [LessonController::class, 'trash'])->name('lesson.trash');
        });
        Route::resource('lesson', LessonController::class);
        Route::delete('file/delete/{id}', [LessonController::class, 'deleteFile'])->name('file.delete');

        /*** Lesson Topic ***/
        Route::group(['prefix' => 'lesson-topic'], static function () {
            Route::put("/{id}/restore", [LessonTopicController::class, 'restore'])->name('lesson-topic.restore');
            Route::delete("/{id}/deleted", [LessonTopicController::class, 'trash'])->name('lesson-topic.trash');
        });
        Route::resource('lesson-topic', LessonTopicController::class);


        /*** Reward system ***/
        Route::group(['prefix' => 'reward'], static function () {

            Route::delete("/{id}/deleted", [RewardController::class, 'deleteReward'])->name('reward.delete');
            Route::get("class-students", [RewardController::class, 'getClassStudent'])->name('reward.class-students');
            Route::post("class-students", [RewardController::class, 'getClassStudent'])->name('reward.class-students');
            

            Route::get('/{id}/edit', [RewardController::class, 'edit'])->name('reward.edit');
            Route::put('reward/{id}', [RewardController::class, 'update'])->name('reward.update');
            Route::get('/points-amount', [RewardController::class, 'getPointsAmount'])->name('reward.points-amount');
            
            // Route::get('/category', [RewardCategoryController::class, 'indexCategory'])->name('reward.index-category');
        });
        Route::resource('reward', RewardController::class);
        Route::post('/clear-ranking',[RewardController::class,'clearRanking'])->name('reward.clear-ranking');

        Route::group(['prefix' => 'reward-category'], static function () {

            Route::get('/category_show', [RewardCategoryController::class, 'category_show'])->name('reward-category.category_show');
            Route::put('restore/{id}', [RewardCategoryController  ::class, 'restore'])->name('reward-category.restore');
            Route::delete('trash/{id}', [RewardCategoryController::class, 'trash'])->name('reward-category.trash');


        });
        Route::resource('reward-category', RewardCategoryController::class);

    
        Route::group(['prefix' => 'reward-redeem'], static function () {

        Route::get("class-students", [RewardRedeemController::class, 'getClassStudent'])->name('reward-redeem.class-students');
        Route::get('/{id}/edit', [RewardRedeemController::class, 'edit'])->name('reward-redeem.edit');
        Route::put('reward/{id}', [RewardRedeemController::class, 'update'])->name('reward-redeem.update');
        });
        Route::resource('reward-redeem', RewardRedeemController::class);
        



        /*** Announcement ***/
        Route::group(['prefix' => 'announcement'], static function () {
            Route::put("/{id}/restore", [AnnouncementController::class, 'restore'])->name('announcement.restore');
            Route::delete("/{id}/deleted", [AnnouncementController::class, 'trash'])->name('announcement.trash');
            Route::delete("file/delete/{id}", [AnnouncementController::class, 'fileDelete'])->name('announcement.fileDelete');
        });
        Route::resource('announcement', AnnouncementController::class);

        Route::resource('document', DocumentController::class);
        Route::delete('document/{id}', [DocumentController::class, 'destroy'])->name('document.destroy');
        Route::delete("document/file/delete/{id}", [DocumentController::class, 'fileDelete'])->name('document.fileDelete');

        /*** Super Admin Announcement ***/
        Route::get('announcement-news-index', [AnnouncementController::class, 'announcementIndex'])->name("announcement-news.index");
        Route::post('announcement-news-store', [AnnouncementController::class, 'storeAnnouncementNews'])->name("announcement-news.store");
        Route::get('announcement-news-show', [AnnouncementController::class, 'showAnnouncementNews'])->name('announcement-news.show');
        Route::put('announcement-news-edit/{id}', [AnnouncementController::class, 'editAnnouncementNews'])->name('announcement-news.edit');
        Route::delete('announcement-news-delete/{id}', [AnnouncementController::class, 'announcementNewDestroy'])->name('announcement-news.delete');

        /*Admin pop up announcement*/
        Route::post('announcement-news-popup', [AnnouncementController::class, 'popupAnnouncementNews'])->name("announcement-news.popup");
        Route::get('announcement-news-popup-show/{school_id}', [DashboardController::class, 'showPopupAnnouncementNews'])->name("announcement-news-popup.show");

        /*** Holiday ***/
        Route::resource('holiday', HolidayController::class);

        /*** Assignment ***/
        // TODO : Improve this
        Route::get('assignment-submission', [AssignmentController::class, 'viewAssignmentSubmission'])->name('assignment.submission');
        Route::put('assignment-submission/{id}', [AssignmentController::class, 'updateAssignmentSubmission'])->name('assignment.submission.update');
        Route::get('assignment-submission-list', [AssignmentController::class, 'assignmentSubmissionList'])->name('assignment.submission.list');
        Route::put("assignment/{id}/restore", [AssignmentController::class, 'restore'])->name('assignment.restore');
        Route::delete("assignment/{id}/deleted", [AssignmentController::class, 'trash'])->name('assignment.trash');
        Route::resource('assignment', AssignmentController::class);

        /*** Sliders ***/
        Route::resource('sliders', SliderController::class);

        /*** Session Years ***/
        Route::group(['prefix' => 'session-year'], static function () {
            Route::put("/{id}/restore", [SessionYearController::class, 'restore'])->name('session-year.restore');
            Route::delete("/{id}/deleted", [SessionYearController::class, 'trash'])->name('session-year.trash');
            Route::put("/{id}/default", [SessionYearController::class, 'default'])->name('session-year.default');
        });
        Route::resource('session-year', SessionYearController::class);


        /*** Exams ***/

        // Grades
        Route::resource('exam/grade', GradeController::class, ['as' => 'exam']);

        // TODO : Improve this
        // Exam Timetables
        Route::resource('exam/timetable', ExamTimetableController::class, ['as' => 'exam']);
        Route::post('exams/update-timetable', [ExamController::class, 'updateExamTimetable'])->name('exams.update-timetable');
        Route::delete('exams/delete-timetable/{id}', [ExamController::class, 'deleteExamTimetable'])->name('exams.delete-timetable');

        //Exam Marks
        Route::post('exams/submit-marks', [ExamController::class, 'submitMarks'])->name('exams.submit-marks');
        Route::get('exams/upload-marks', [ExamController::class, 'uploadMarks'])->name('exams.upload-marks');
        Route::get('exams/marks-list', [ExamController::class, 'marksList'])->name('exams.marks-list');

        // Exam Result
        Route::get('exams/exam-result', [ExamController::class, 'getExamResultIndex'])->name('exams.get-result');
        Route::get('exams/show-result', [ExamController::class, 'showExamResult'])->name('exams.show-result');
        Route::post('exams/update-result-marks', [ExamController::class, 'updateExamResultMarks'])->name('exams.update-result-marks');
        Route::get('exams/result/student/{student_id}/exam/{exam_id}', [ExamController::class, 'examResultPdf']);

        // Exams
        Route::get('exams/get-subjects/{exam_id}', [ExamController::class, 'getSubjectByExam'])->name('exams.subject');
        Route::post('exams/publish/{id}', [ExamController::class, 'publishExamResult'])->name('exams.publish');
        Route::put("exams/{id}/restore", [ExamController::class, 'restore'])->name('exams.restore');
        Route::delete("exams/{id}/deleted", [ExamController::class, 'trash'])->name('exams.trash');

        Route::get('exams/result-report/{session_year_id}/{exam_name}', [ExamController::class, 'resultReport']);
        Route::get('exams/timetable', [ExamController::class, 'examTimetableIndex'])->name('exams.timetable');
        Route::get('exams/timetable/{id?}', [ExamController::class, 'examTimetableShow'])->name('exams.timetable.show');

        Route::resource('exams', ExamController::class);

        // TODO make two groups promote student and transfer student and classify the routes related to their group
        Route::resource('promote-student', PromoteStudentController::class);
        Route::get('getPromoteData', [PromoteStudentController::class, 'getPromoteData']);
        Route::post('transfer-student-store', [PromoteStudentController::class, 'storeTransferStudent'])->name('transfer-student.store');
        Route::get('transfer-student-list', [PromoteStudentController::class, 'showTransferStudent'])->name('transfer-student.show');
        Route::get('promote-student-history',[PromoteStudentController::class,'showPromoteStudentHistory'])->name('promote-student.history');

        // TODO : Improve this
        /*** Language ***/
        Route::get('language-sample', [LanguageController::class, 'language_sample']);
        Route::get('language-json-file/{code?}', [LanguageController::class, 'language_file'])->name('language.json.file');

        Route::get('language-list', [LanguageController::class, 'show']);
        Route::resource('language', LanguageController::class);

        Route::group(['prefix' => 'fees-type'], static function () {
            Route::put("/{id}/restore", [FeesTypeController::class, 'restore'])->name('fees-type.restore');
            Route::delete("/{id}/deleted", [FeesTypeController::class, 'trash'])->name('fees-type.trash');
        });
        Route::resource('fees-type', FeesTypeController::class);


        Route::group(['prefix' => 'student-fee-types'], static function () {
            Route::delete("/{id}/deleted", [StudentFeeTypesController::class, 'deleteFeeType'])->name('student-fee-types.delete');
            Route::get("class-students", [StudentFeeTypesController::class, 'getClassStudent'])->name('student-fee-types.class-students');
            Route::get("session-year-class", [StudentFeeTypesController::class, 'getSessionYearClass'])->name('student-fee-types.session-year-class');
            Route::get("/credit-note-einvoice-submit/{id}", [StudentFeeTypesController::class, 'submitCreditEInvoicing'])->name('student-fee-types.submitCreditEinvoice');
            Route::put("/credit-note-einvoice-cancel/{id}", [StudentFeeTypesController::class, 'cancelCreditEInvoicing'])->name('student-fee-types.cancelCreditEinvoice');
            Route::get('/check-credit-note-e-invoice-status/{id}', [StudentFeeTypesController::class, 'getCreditNoteEInvoiceStatus'])->name('student-fee-types.check-credit-note-e-invoice-status');

            Route::get('/credit-note', [StudentFeeTypesController::class, 'creditNoteIndex'])->name('student-fee-types.credit-note');
            Route::get('/debit-note', [DebitNoteController::class, 'debitNoteIndex'])->name('student-fee-types.debit-note');
            
            Route::get('/debit-note-einvoice-submit/{id}', [DebitNoteController::class, 'submitDebitEInvoicing'])->name('student-fee-types.submitDebitEinvoice');
            Route::put('/debit-note-einvoice-cancel/{id}', [DebitNoteController::class, 'cancelDebitEInvoicing'])->name('student-fee-types.cancelDebitEinvoice');
            Route::get('/check-debit-note-e-invoice-status/{id}', [DebitNoteController::class, 'getDebitNoteEInvoiceStatus'])->name('student-fee-types.check-debit-note-e-invoice-status');


            Route::get('/refund-note-einvoice-submit/{id}', [RefundNoteController::class, 'submitRefundEInvoicing'])->name('student-fee-types.submitRefundEinvoice');
            Route::put('/refund-note-einvoice-cancel/{id}', [RefundNoteController::class, 'canceltRefundEInvoicing'])->name('student-fee-types.cancelRefundEinvoice');
            Route::get('/check-refund-note-e-invoice-status/{id}', [RefundNoteController::class, 'getRefundNoteEInvoiceStatus'])->name('student-fee-types.check-refund-note-e-invoice-status');


            Route::get('/debit-note/get-invoice', [DebitNoteController::class, 'debitNoteGetInvoice'])->name('student-fee-types.debit-note-invoice');
            Route::get('/credit-note/get-invoice', [StudentFeeTypesController::class, 'creditNoteGetInvoice'])->name('student-fee-types.credit-note-invoice');
            Route::post('/credit-note-store', [StudentFeeTypesController::class, 'creditNoteStore'])->name('student-fee-types.credit-note-store');
            Route::post('/debit-note-store', [DebitNoteController::class, 'debitNoteStore'])->name('student-fee-types.debit-note-store');
            Route::get('/credit-note-show', [StudentFeeTypesController::class, 'creditNoteShow'])->name('student-fee-types.credit-note-show');
            Route::get('/debit-note-show', [DebitNoteController::class, 'debitNoteShow'])->name('student-fee-types.debit-note-show');
            Route::get('/credit-note-edit/{id}', [StudentFeeTypesController::class, 'creditNoteEdit'])->name('student-fee-types.credit-note-edit');
            Route::get('/debit-note-edit/{id}', [DebitNoteController::class, 'debitNoteEdit'])->name('student-fee-types.debit-note-edit');
            Route::put('/credit-note-update/{id}', [StudentFeeTypesController::class, 'creditNoteUpdate'])->name('student-fee-types.credit-note-update');
            Route::put('/debit-note-update/{id}', [DebitNoteController::class, 'debitNoteUpdate'])->name('student-fee-types.debit-note-update');
            Route::delete('/credit-note-detail/{id}/deleted', [StudentFeeTypesController::class, 'creditNoteDetailDelete']);
            Route::delete('/debit-note-detail/{id}/deleted', [DebitNoteController::class, 'debitNoteDetailDelete']);
            Route::delete('/credit-note-delete/{id}', [StudentFeeTypesController::class, 'creditNoteDelete'])->name('student-fee-types.credit-note-delete');
            Route::delete('/debit-note-delete/{id}', [DebitNoteController::class, 'debitNoteDelete'])->name('student-fee-types.debit-note-delete');
            Route::put('/credit-note-restore/{id}', [StudentFeeTypesController::class, 'creditNoteRestore'])->name('student-fee-types.credit-note-restore');
            Route::put('/debit-note-restore/{id}', [DebitNoteController::class, 'DebitNoteRestore'])->name('student-fee-types.debit-note-restore');
            Route::post('/credit-note-status', [StudentFeeTypesController::class, 'creditNoteStatus'])->name('student-fee-types.credit-note-status');
            Route::post('/debit-note-status', [DebitNoteController::class, 'debitNoteStatus'])->name('student-fee-types.debit-note-status');
            Route::get('/credit-note-pdf/{id}', [StudentFeeTypesController::class, 'creditNotePdf'])->name('student-fee-types.credit-note-pdf');
            Route::get('/debit-note-pdf/{id}', [DebitNoteController::class, 'debitNotePdf'])->name('student-fee-types.debit-note-pdf');

            Route::get('/refund-note', [RefundnoteController::class, 'refundNoteIndex'])->name('student-fee-types.refund-note');
            Route::get('/refund-note/get-invoice', [RefundnoteController::class, 'refundNoteGetInvoice'])->name('student-fee-types.refund-note-invoice');
            Route::post('/refund-note-store', [RefundnoteController::class, 'refundNoteStore'])->name('student-fee-types.refund-note-store');
            Route::get('/refund-note-show', [RefundnoteController::class, 'refundNoteShow'])->name('student-fee-types.refund-note-show');
            Route::get('/refund-note-edit/{id}', [RefundnoteController::class, 'refundNoteEdit'])->name('student-fee-types.refund-note-edit');
            Route::put('/refund-note-update/{id}', [RefundnoteController::class, 'refundNoteUpdate'])->name('student-fee-types.refund-note-update');
            Route::delete('/refund-note-detail/{id}/deleted', [RefundnoteController::class, 'refundNoteDetailDelete']);
            Route::delete('/refund-note-delete/{id}', [RefundnoteController::class, 'refundNoteDelete'])->name('student-fee-types.refund-note-delete');
            Route::put('/refund-note-restore/{id}', [RefundnoteController::class, 'refundNoteRestore'])->name('student-fee-types.refund-note-restore');
            Route::post('/refund-note-status', [RefundnoteController::class, 'refundNoteStatus'])->name('student-fee-types.refund-note-status');
            Route::get('/refund-note-pdf/{id}', [RefundnoteController::class, 'refundNotePdf'])->name('student-fee-types.refund-note-pdf');

            Route::get('/item-code', [ItemCodeController::class, 'itemCodeIndex'])->name('student-fee-types.item-code');
            Route::post('/item-code/store', [ItemCodeController::class, 'itemCodeStore'])->name('student-fee-types.item-code-store');
            Route::get('/item-code/show', [ItemCodeController::class, 'itemCodeShow'])->name('student-fee-types.item-code-show');
            Route::get('/item-code/edit/{id?}', [ItemCodeController::class, 'itemCodeEdit'])->name('student-fee-types.item-code-edit');
            Route::post('/item-code/update/{id}', [ItemCodeController::class, 'itemCodeUpdate'])->name('student-fee-types.item-code-update');
            Route::delete('/item-code/delete/{id}', [ItemCodeController::class, 'itemCodeDestroy'])->name('student-fee-types.item-code-destroy');

            Route::get("download-import-template/{class_id}", [StudentFeeTypesController::class, 'downloadStudentFeeTypeTemplate'])->name("student-fee-types.download.import.template");
            Route::post("do-import/{class_id}", [StudentFeeTypesController::class, 'importStudentFeeType'])->name("student-fee-types.import");
        });
        Route::resource('student-fee-types', StudentFeeTypesController::class);

        Route::group(['prefix' => 'fees'], static function () {
            // Fees
            Route::put("/{id}/restore", [FeesController::class, 'restore'])->name('fees.restore');
            Route::delete("/{id}/delete", [FeesController::class, 'trash'])->name('fees.trash');
            Route::delete("/installment/{id}", [FeesController::class, 'deleteInstallment'])->name('fees.installment.delete');
            Route::delete("/class-type/{id}", [FeesController::class, 'deleteClassType'])->name('fees.class-type.delete');
            Route::get("/search", [FeesController::class, 'search'])->name('fees.search');


            // Fees Paid
            Route::get('/paid', [FeesController::class, 'feesPaidListIndex'])->name('fees.paid.index');
            Route::get('/paid/list', [FeesController::class, 'feesPaidList'])->name('fees.paid.list');

            Route::get('/pay/compulsory/{feesID}/{studentID}', [FeesController::class, 'payCompulsoryFeesIndex'])->name('fees.compulsory.index');
            Route::post('pay/compulsory', [FeesController::class, 'payCompulsoryFeesStore'])->name('fees.compulsory.store');

            // Optional Fees Payment Offline
            Route::get('/pay/optional/{feesID}/{studentID}', [FeesController::class, 'payOptionalFeesIndex'])->name('fees.optional.index');
            Route::post('pay/optional', [FeesController::class, 'payOptionalFeesStore'])->name('fees.optional.store');

            Route::post('/paid/store', [FeesController::class, 'feesPaidStore'])->name('fees.paid.store');
            Route::put('/paid/update/{id}', [FeesController::class, 'feesPaidUpdate'])->name('fees.paid.update');
            Route::delete('/paid/remove-optional-fee/{id}', [FeesController::class, 'removeOptionalFees'])->name('fees.paid.remove.optional.fees');
            Route::delete('/paid/remove-installment-fees/{id}', [FeesController::class, 'removeInstallmentFees'])->name('fees.paid.remove.installment.fees');
            // Fees Config
            Route::get('/config', [FeesController::class, 'feesConfigIndex'])->name('fees.config.index');
            Route::post('/config/update', [FeesController::class, 'feesConfigUpdate'])->name('fees.config.update');

            Route::post('/optional-paid/store', [FeesController::class, 'optionalFeesPaidStore'])->name('fees.optional-paid.store');


            // Transaction list
            Route::get('/transaction-logs', [FeesController::class, 'feesTransactionsLogsIndex'])->name('fees.transactions.log.index');
            Route::get('/transaction-logs/list', [FeesController::class, 'feesTransactionsLogsList'])->name('fees.transactions.log.list');

            // Receipt
            Route::get('/paid/receipt-pdf/{id}', [FeesController::class, 'feesPaidReceiptPDF'])->name('fees.paid.receipt.pdf');
        });

        Route::group(['prefix' => 'student-fees'], static function () {

            Route::put("/{id}/restore", [StudentFeesController::class, 'restore'])->name('student-fees.restore');
            Route::delete("/{id}/trash", [StudentFeesController::class, 'trash'])->name('student-fees.trash');
            Route::delete("/student-fees-detail/{id}", [StudentFeesController::class, 'deleteStudentFeeDetail'])->name('student-fees.detail.delete');

            Route::post("set-student-fee-status", [StudentFeesController::class, 'setStudentFeeStatus'])->name('set-student-fee-status');
            Route::post("submit-multiple-einvoice", [StudentFeesController::class, 'submitMultipleEInvoice'])->name('submit-multiple-einvoice');
            Route::post("consolidate-invoice", [StudentFeesController::class, 'consolidateInvoice'])->name('consolidate-invoice');

            Route::get('/paid', [StudentFeesController::class, 'studentFeesPaidListIndex'])->name('student-fees.paid.index');
            Route::get('/paid/list', [StudentFeesController::class, 'studentFeesPaidList'])->name('student-fees.paid.list');
            Route::post("set-student-fee-pending-status", [StudentFeesController::class, 'setStudentFeePendingStatus'])->name('set-student-fee-pending-status');

            Route::get('/pay/compulsory/{studentFeesID}/{studentID}', [StudentFeesController::class, 'payCompulsoryFeesIndex'])->name('student-fees.compulsory.index');
            Route::post('pay/compulsory', [StudentFeesController::class, 'payCompulsoryFeesStore'])->name('student-fees.compulsory.store');

            Route::get('/paid/receipt-pdf/{id}/{filename?}/{isPaid?}', [StudentFeesController::class, 'feesPaidReceiptPDF'])->name('student-fees.paid.receipt.pdf');

            Route::get('fee-paid-date-edit',[StudentFeesController::class,'feePaidDateEdit'])->name('student-fees.paid-date.edit');
            Route::post('fee-paid-date-update',[StudentFeesController::class,'feePaidDateUpdate'])->name('student-fees.paid-date.update');

            Route::get("/search", [StudentFeesController::class, 'search'])->name('student-fees.search');
            Route::get("fee-classes", [StudentFeesController::class, 'getFeeClass'])->name('student-fees.fee-classes');
            Route::get("class-students", [StudentFeesController::class, 'getClassStudent'])->name('student-fees.class-students');
            Route::get("document-class-students", [StudentFeesController::class, 'getDocumentClassStudent'])->name('student-fees.document-class-students');

            Route::get("/session-classes", [StudentFeesController::class, 'getSessionClass'])->name('student-fees.session-class');

            Route::post("student-fee-summary", [StudentFeesController::class, 'studentFeeSummary'])->name('student-fees.student-fee-summary');

            // reports
            Route::get('/fees-collection-report', [StudentFeesController::class, 'feesCollectionReportIndex'])->name('student-fees.fees-collection-report.index');
            Route::get('/fees-collection-report/list', [StudentFeesController::class, 'feesCollectionReportList'])->name('student-fees.fees-collection-report.list');

            Route::get('/fees-pending-report', [StudentFeesController::class, 'feesPendingReportIndex'])->name('student-fees.fees-pending-report.index');
            Route::get('/fees-pending-report/list', [StudentFeesController::class, 'feesPendingReportList'])->name('student-fees.fees-pending-report.list');

            Route::get('/fees-outstanding-report', [StudentFeesController::class, 'feesOutstandingReportIndex'])->name('student-fees.fees-outstanding-report.index');
            Route::get('/fees-outstanding-report/list', [StudentFeesController::class, 'feesOutstandingReportList'])->name('student-fees.fees-outstanding-report.list');

            Route::get('/fees-student-report', [StudentFeesController::class, 'feesStudentReportIndex'])->name('student-fees.fees-student-report.index');
            Route::get('/fees-student-report/list', [StudentFeesController::class, 'feesStudentReportList'])->name('student-fees.fees-student-report.list');

            Route::get('/fees-type-report', [StudentFeesController::class, 'feesTypeReportIndex'])->name('student-fees.fees-type-report.index');
            Route::get('/fees-type-report/list', [StudentFeesController::class, 'feesTypeReportList'])->name('student-fees.fees-type-report.list');

            Route::get('/fees-class-report', [StudentFeesController::class, 'feesClassReportIndex'])->name('student-fees.fees-class-report.index');
            Route::get('/fees-class-report/list', [StudentFeesController::class, 'feesClassReportList'])->name('student-fees.fees-class-report.list');

            Route::get('/fees-all-report',[StudentFeesController::class, 'feesAllReportIndex'])->name('student-fees.fees-all-report.index');
            Route::get('/fees-all-report/list',[StudentFeesController::class, 'feesAllReportList'])->name('student-fees.fees-all-report.list');

            Route::get('/setup-uid', [StudentFeesController::class, 'setupUID']);

            Route::get('/submit-e-invoicing/{id}',[StudentFeesController::class,'submitEInvoicing'])->name('student-fees.submit-e-invoicing');
            Route::put('/cancel-e-invoicing/{id}',[StudentFeesController::class,'cancelEInvoicing'])->name('student-fees.cancel-e-invoicing');

            Route::get('/recurring-fees', [RecurringFeesController::class, 'index'])->name('student-fee.recurring-fees');
            Route::get('/recurring-fees/list', [RecurringFeesController::class, 'list'])->name('student-fee.recurring-fees.list');
            Route::post('/recurring-fees/disable/{id}', [RecurringFeesController::class, 'disable'])->name('student-fee.recurring-fees.disable');

            Route::get('/check-e-invoice-status/{id}', [StudentFeesController::class, 'getEInvoiceStatus'])->name('student-fees.check-e-invoice-status');
        });
        Route::resource('fees', FeesController::class);
        Route::resource('student-fees', StudentFeesController::class);

        // Statement    
        Route::resource("statement",StatementController::class);
        Route::get('/statement-pdf/{id}', [StatementController::class, 'showPdf'])->name('statement-pdf');
    
        



        // Expense
        Route::get('expense/filter/{session_year_id?}', [ExpenseController::class, 'filter_graph']);
        Route::resource('expense', ExpenseController::class);

        //Credit
        Route::group(['prefix' => 'credit'], static function () {


            Route::get("/class-students/{id?}", [CreditController::class, 'getStudent'])->name('credit.class-students');
            //Route::post("/class-students", [CreditController::class,'getStudent'])->name('credit.class-students');

            Route::get('/{id}/edit', [CreditController::class, 'edit'])->name('credit.edit');
            Route::put('/{id}', [CreditController::class, 'update'])->name('credit.update');
            Route::delete("/{id}/deleted", [CreditController::class, 'deleteCredit'])->name('credit.delete');

            Route::get("view-credit/", [CreditController::class, 'viewCreditIndex'])->name('credit.view-credit');
            Route::get("view-credit-show/{id}", [CreditController::class, 'viewCreditShow'])->name('credit.view-credit-show');
            // Route::get("view-credit/students", [CreditController::class,'getStudent'])->name('credit.viewCredit.student');

            Route::get('/manage-credit/{id?}', [CreditController::class, 'manageCreditIndex'])->name('credit.manage-credit');
            Route::get('/manage-credit-show/{id?}', [CreditController::class, 'manageCreditShow'])->name('credit.manage-credit-show');
        });
        Route::resource('credit', CreditController::class);

        // Online Exam
        Route::group(['prefix' => 'online-exam'], static function () {
            Route::put("/{id}/restore", [OnlineExamController::class, 'restore'])->name('online-exam.restore');
            Route::delete("/{id}/deleted", [OnlineExamController::class, 'trash'])->name('online-exam.trash');
            Route::get('/add-questions-index/{id}', [OnlineExamController::class, 'addQuestionIndex'])->name('online-exam.add.questions.index');
            Route::post('/add-new-question', [OnlineExamController::class, 'storeExamQuestionChoices'])->name('online-exam.add-new-question');
            Route::get('/get-class-questions/{id}', [OnlineExamController::class, 'getClassQuestions'])->name('online-exam-question.get-class-questions');
            Route::post('/store-questions-choices', [OnlineExamController::class, 'storeQuestionsChoices'])->name('online-exam.store-choice-question');
            Route::delete('/remove-choiced-question/{id}', [OnlineExamController::class, 'removeQuestionsChoices'])->name('online-exam.remove-choice-question');
            Route::get('/result/{id}', [OnlineExamController::class, 'onlineExamResultIndex'])->name('online-exam.result.index');
            Route::get('/result-show/{id}', [OnlineExamController::class, 'showOnlineExamResult'])->name('online-exam.result.show');
            Route::get('/detailed-result/{id}', [OnlineExamController::class, 'showDetailedExamResult'])->name('online-exam.detailed-result');
        });
        Route::resource('online-exam', OnlineExamController::class);

        Route::group(['prefix' => 'online-exam-question'], static function () {
            Route::delete('/remove-option/{id}', [OnlineExamQuestionController::class, 'removeOptions']);
        });
        Route::resource('online-exam-question', OnlineExamQuestionController::class);
        // End Online Exam Routes

        Route::get('user-groups/restore/{id}', [UserGroupController::class, 'restore'])->name('usergroup.restore');
        Route::get('user-groups/trash/{id}', [UserGroupController::class, 'trash'])->name('usergroup.trash');
        Route::put('user-groups/update/{id}', [UserGroupController::class, 'update'])->name('usergroup.update');
        Route::delete('user-groups/destroy/{id}', [UserGroupController::class, 'destroy'])->name('usergroup.destroy');
        Route::get('user-groups/show/{id}', [UserGroupController::class, 'show'])->name('user-group.show');
        Route::get('user-groups', [UserGroupController::class, 'index'])->name('user-groups.index');
        Route::post('user-groups/store', [UserGroupController::class, 'store'])->name('user-group.store');
        Route::get("user-groups/getclasssubject", [UserGroupController::class, 'getClassSubject'])->name('user-group.subject');


        /*** Commission ***/
        Route::resource('section', SectionController::class);
        Route::get('commission', [CommissionController::class, 'index'])->name('commission.index');
        Route::get('commission/show', [CommissionController::class, 'show'])->name('commission.show');
        Route::post('commission/store', [CommissionController::class, 'store'])->name('commission.store');
        Route::put('commission/update/{id}', [CommissionController::class, 'updatecommission'])->name('commission.update');
        Route::delete('commission/destroy/{id}', [CommissionController::class, 'destroy'])->name('commission.destroy');
        Route::put('commission/restore/{id}', [CommissionController::class, 'restore'])->name('commission.restore');
        Route::delete('commission/trash/{id}', [CommissionController::class, 'trash'])->name('commission.trash');



        /*** System Settings ***/
        Route::group(['prefix' => 'system-settings'], static function () {
            Route::get('fcm', [SystemSettingsController::class, 'fcmIndex'])->name('system-settings.fcm');
            Route::get('privacy-policy', [SystemSettingsController::class, 'privacyPolicy'])->name('system-settings.privacy-policy');
            Route::get('terms-condition', [SystemSettingsController::class, 'termsConditions'])->name('system-settings.terms-condition');
            Route::get('attendance', [SystemSettingsController::class, 'attendance'])->name('system-settings.attendance');
            Route::get('contact-us', [SystemSettingsController::class, 'contactUs'])->name('system-settings.contact-us');
            Route::get('about-us', [SystemSettingsController::class, 'aboutUs'])->name('system-settings.about-us');
            Route::put('notification-settings', [SystemSettingsController::class, 'notificationSettingUpdate'])->name('notification-setting.update');

            /*** Email Settings ***/
            Route::get('email', [SystemSettingsController::class, 'emailIndex'])->name('system-settings.email.index');
            Route::post('email', [SystemSettingsController::class, 'emailUpdate'])->name('system-settings.email.update');
            Route::post('email/verify', [SystemSettingsController::class, 'verifyEmailConfiguration'])->name('system-settings.email.verify');

            Route::get('email-template', [SystemSettingsController::class, 'emailTemplate'])->name('system-settings.email.template');

            /*** App Settings ***/
            Route::get('app', [SystemSettingsController::class, 'appSettingsIndex'])->name('system-settings.app');
            Route::post('app', [SystemSettingsController::class, 'appSettingsUpdate'])->name('system-settings.app.update');

            /*** Payment Settings ***/
            Route::get('payment', [SystemSettingsController::class, 'paymentIndex'])->name('system-settings.payment.index');
            Route::post('payment', [SystemSettingsController::class, 'paymentUpdate'])->name('system-settings.payment.update');

            Route::get('third-party-apis', [SystemSettingsController::class, 'thirdPartyApiIndex'])->name('system-settings.third-party');
            Route::post('third-party-apis', [SystemSettingsController::class, 'thirdPartyApiUpdate'])->name('system-settings.third-party.update');

            Route::get('subscription-settings', [SystemSettingsController::class, 'subscription_settings'])->name('system-settings.subscription-settings');
            Route::post('subscription-settings', [SystemSettingsController::class, 'subscription_settings_update'])->name('system-settings.subscription-settings-store');

            Route::get('rfid-whitelist', [SystemSettingsController::class, 'rfidWhitelist'])->name('system-settings.rfid-whitelist');
            Route::post('rfid-whitelist/store', [SystemSettingsController::class, 'rfidStore'])->name('rfid.store');
            Route::get('rfid-whitelist/show', [SystemSettingsController::class, 'rfidShow'])->name('rfid.show');
            Route::post('rfid-whitelist/change-status/{id}', [SystemSettingsController::class, 'rfidChangeStatus'])->name('rfid.change-status');
            Route::delete('rfid-whitelist/{id}/deleted', [SystemSettingsController::class, 'rfidTrash'])->name('rfid.trash');

            Route::get('school-terms-conditions', [SystemSettingsController::class, 'school_terms_condition'])->name('system-settings.school-terms-condition');
        });

        Route::resource('system-settings', SystemSettingsController::class);

        /*** School Settings ***/
        Route::group(['prefix' => 'school-settings'], static function () {
            Route::get('online-exam', [SchoolSettingsController::class, 'onlineExamIndex'])->name('school-settings.online-exam.index');
            Route::post('online-exam', [SchoolSettingsController::class, 'onlineExamStore'])->name('school-settings.online-exam.store');
            Route::get('id-card/remove/{type}', [SchoolSettingsController::class, 'remove_image_from_id_card']);
            Route::get('terms-condition', [SchoolSettingsController::class, 'terms_condition'])->name('school-settings.terms-condition');
            Route::get('privacy-pilicy', [SchoolSettingsController::class, 'privacy_policy'])->name('school-settings.privacy-policy');

            Route::get('email-template', [SchoolSettingsController::class, 'emailTemplate'])->name('school-settings.email.template');
            Route::put('email-template', [SchoolSettingsController::class, 'emailTemplateUpdate'])->name('school-settings.email-template.update');

            Route::get('e-invoice-settings',[SchoolSettingsController::class,'eInvoiceSettings'])->name('school-settings.e-invoice.index');
            Route::post('e-invoice-settings/update',[SchoolSettingsController::class,'eInvoiceSettingsUpdate'])->name('school-settings.e-invoice.update');
            
            Route::get('third-party-apis', [SchoolSettingsController::class, 'thirdPartyApiIndex'])->name('school-settings.third-party');
            Route::post('third-party-apis', [SchoolSettingsController::class, 'thirdPartyApiUpdate'])->name('school-settings.third-party.update');

            Route::post('search-taxpayer-tin', [SchoolSettingsController::class, 'searchTaxPayerTin'])->name('school-settings.search-tin');
            Route::post('validate-taxpayer-tin', [SchoolSettingsController::class, 'validateTaxPayerTin'])->name('school-settings.validate-tin');

        });
        Route::resource('school-settings', SchoolSettingsController::class);

        Route::get('system-update', [SystemUpdateController::class, 'index'])->name('system-update.index');
        Route::post('system-update', [SystemUpdateController::class, 'update'])->name('system-update.update');

        /*** School ***/
        Route::group(['prefix' => 'schools'], static function () {
            Route::put("/{id}/restore", [SchoolController::class, 'restore'])->name('schools.restore');
            Route::delete("/{id}/deleted", [SchoolController::class, 'trash'])->name('schools.trash');
            // Route::get('/admin/search', [SchoolController::class, 'adminSearch']);
            Route::post('/admin/update', [SchoolController::class, 'updateAdmin']);
            Route::PUT('/change/status/{id}', [SchoolController::class, 'changeStatus']);
            Route::get('/admin/search', [SchoolController::class, 'searchAdmin']);
            Route::get('/schoolBranch/{id}', [SchoolController::class, 'editSchoolBranch'])->name('school.branch');
            Route::post('/schoolBranch/update', [SchoolController::class, 'updateSchoolBranch'])->name('update.school.branch');
            Route::delete("/schoolBranch/{id}/delete", [SchoolController::class, 'deleteSchoolBranch'])->name('delete.school.branch');
            Route::get('/validate-einvoice/{id}', [SchoolController::class, 'validateEInvoicing'])->name('schools.validate-einvoice');

        });
        Route::resource('schools', SchoolController::class);

        /*** Form Fields ***/
        Route::group(['prefix' => 'form-fields'], static function () {
            Route::post('/update-rank', [FormFieldsController::class, 'updateRankOfFields']);
            Route::put("/{id}/restore", [FormFieldsController::class, 'restore'])->name('form-fields.restore');
            Route::delete("/{id}/deleted", [FormFieldsController::class, 'trash'])->name('form-fields.trash');
        });
        Route::resource('form-fields', FormFieldsController::class);

        /*** Package ***/
        Route::group(['prefix' => 'package'], static function () {
            Route::get('status/{id}', [PackageController::class, 'status']);
            Route::put('restore/{id}', [PackageController::class, 'restore'])->name('package.restore');
            Route::delete('trash/{id}', [PackageController::class, 'trash'])->name('package.trash');
            Route::PATCH('change/rank', [PackageController::class, 'change_rank']);
        });
        Route::resource('package', PackageController::class);

        // Features
        Route::get('features', [PackageController::class, 'features_list']);
        Route::get('features/show', [PackageController::class, 'features_show'])->name('features.show');

        // Subscription
        Route::group(['prefix' => 'subscriptions'], static function () {
            Route::get('plan/{id}/type/{type}/current-plan/{isCurrentPlan?}', [SubscriptionController::class, 'plan']);
            Route::get('prepaid/package/{package_id}/{type?}/{isCurrentPlan?}', [SubscriptionController::class, 'prepaid_plan']);

            Route::get('history', [SubscriptionController::class, 'history'])->name('subscriptions.history');
            Route::get('cancel-upcoming/{id?}', [SubscriptionController::class, 'cancel_upcoming'])->name('subscriptions.cancel.upcoming');
            Route::get('confirm-upcoming-plan/{id}', [SubscriptionController::class, 'confirm_upcoming_plan']);

            Route::get('payment/success/{checkout_session_id}/{subscriptionBill_id?}/{package_id?}/{type?}/{subscription_id?}/{isCurrentPlan?}', [SubscriptionController::class, 'payment_success']);
            Route::get('payment/cancel/{subscriptionBillId?}', [SubscriptionController::class, 'payment_cancel']);

            Route::get('bill/receipt/{id}', [SubscriptionController::class, 'bill_receipt']);
            Route::get('report', [SubscriptionController::class, 'subscription_report']);
            Route::get('report/show/{status?}', [SubscriptionController::class, 'subscription_report_show']);
            Route::put('update-expiry', [SubscriptionController::class, 'update_expiry'])->name('subscription.update.expiry');
            Route::put('change-bill-date', [SubscriptionController::class, 'change_bill_date'])->name('subscription.change.bill.date');
            Route::get('start-immediate-plan/{id?}/type/{type?}', [SubscriptionController::class, 'start_immediate_plan']);
            Route::put('update-current-plan', [SubscriptionController::class, 'update_current_plan'])->name('subscription.update-current-plan');
            Route::get('generate-bill/{id?}', [SubscriptionController::class, 'generate_bill']);
            Route::get('transactions', [SubscriptionController::class, 'transactions_log']);
            Route::get('transactions/list', [SubscriptionController::class, 'subscription_transaction_list']);

            Route::get('bill-payment/{id}', [SubscriptionController::class, 'bill_payment']);
            Route::put('bill-payment/store{id?}', [SubscriptionController::class, 'bill_payment_store'])->name('subscriptions-bill-payment.update');

            // bill-payment/edit

            Route::delete('bill-payment/destroy/{id}', [SubscriptionController::class, 'delete_bill_payment']);

            Route::get('pay-prepaid-upcoming-plan/{package_id}/type/{type}/subscription/{subscription_id}', [SubscriptionController::class, 'pay_prepaid_upcoming_plan']);

            // Super admin graph
            Route::get('transaction/{year}', [SubscriptionController::class, 'transaction']);

            // Razorpay
            Route::post('create/order-id', [SubscriptionController::class, 'razorpay_order_id']);
            Route::post('razorpay', [SubscriptionController::class, 'razorpay']);
        });
        Route::resource('subscriptions', SubscriptionController::class);

        // Addons
        Route::group(['prefix' => 'addons'], static function () {
            Route::put('restore/{id}', [AddonController::class, 'restore'])->name('addons.restore');
            Route::delete('trash/{id}', [AddonController::class, 'trash'])->name('addons.trash');
            Route::put('status/{id}', [AddonController::class, 'status'])->name('addons.status');
            Route::get('plan', [AddonController::class, 'plan'])->name('addons.plan');
            Route::get('subscribe/{id}/package-type/{type}', [AddonController::class, 'subscribe'])->name('addons.subscribe');
            Route::get('discontinue/{id}', [AddonController::class, 'discontinue'])->name('addons.discontinue');

            Route::get('prepaid-package/{id}', [AddonController::class, 'prepaid_package_addon'])->name('prepaid_package_addon');

            Route::get('payment/success/{checkout_session_id}/{id}', [AddonController::class, 'payment_success']);
            Route::get('payment/cancel', [AddonController::class, 'payment_cancel']);
        });
        Route::resource('addons', AddonController::class);

        // Expense Category
        Route::group(['prefix' => 'expense-category'], static function () {
            Route::put('restore/{id}', [ExpenseCategoryController::class, 'restore'])->name('expense-category.restore');
            Route::delete('trash/{id}', [ExpenseCategoryController::class, 'trash'])->name('expense-category.trash');
        });
        Route::resource('expense-category', ExpenseCategoryController::class);

        // financial report
        // Route::group(['prefix' => 'financial-report'], static function () {
        //     Route::get('/fees-collection-report', [StudentFeesController::class, 'feesCollectionReportIndex'])->name('student-fees.fees-collection-report.index');
        //     Route::get('/fees-collection-report/list', [StudentFeesController::class, 'feesCollectionReportList'])->name('student-fees.fees-collection-report.list');

        // });
        Route::resource('financial-report', FinancialReportController::class);

        // Expense
        Route::get('expense/filter/{session_year_id?}', [ExpenseController::class, 'filter_graph']);
        Route::resource('expense', ExpenseController::class);

        // Payroll
        Route::get('payroll/slip/{id?}', [PayrollController::class, 'slip'])->name('payroll.slip');
        Route::get('payroll/slips', [PayrollController::class, 'slip_index'])->name('payroll.slip.index');
        Route::get('payroll/slips/list', [PayrollController::class, 'slip_list'])->name('payroll.slip.list');
        Route::delete('payroll/{id}', [PayrollController::class, 'destroy'])->name('payroll.destroy');
        Route::post('payroll/preview', [PayrollController::class, 'preview'])->name('payroll.preview');


        Route::resource('payroll', PayrollController::class)->only(['index', 'store', 'show']);

        Route::post('/update-epf-contribution', [PayrollController::class, 'updateEpfContribution']);

        Route::get('payroll/custom-payroll', [PayrollController::class, 'customIndex'])->name('customPayroll.index');
        Route::get('payroll/custom-payroll-show', [PayrollController::class, 'customShow'])->name('customPayroll.show');
        Route::get('payroll/custom-payroll-update/{id}', [PayrollController::class, 'customUpdate'])->name('customPayroll.update');
        Route::delete('payroll/custom-payroll-delete/{id}', [PayrollController::class, 'customDestroy'])->name('customPayroll.destroy');

        // Leave
        Route::group(['prefix' => 'leave'], static function () {
            Route::get('request', [LeaveController::class, 'leave_request'])->name('leave.request');
            Route::get('request/show', [LeaveController::class, 'leave_request_show'])->name('leave.request.show');
            Route::put('status/update', [LeaveController::class, 'leave_status_update'])->name('leave.status.update');
            Route::get('filter', [LeaveController::class, 'filter_leave']);
            Route::get('report', [LeaveController::class, 'report'])->name('leave.report');
            Route::get('detail', [LeaveController::class, 'detail'])->name('leave.detail');
            Route::get('leave_report', [LeaveController::class, 'leave_report'])->name('leave.leave_report');
            Route::get('leave_detail', [LeaveController::class, 'leave_detail'])->name('leave.leave_detail');
            Route::get('available/{id}', [LeaveController::class, 'checkLeaveAvailable']);
        });

        Route::get('leave-master/category', [LeaveMasterController::class, 'leaveCategoryIndex'])->name('leave-master.category-index');
        Route::post('leave-master/category-store', [LeaveMasterController::class, 'leaveCategoryStore'])->name('leave-master.category-store');
        Route::get('leave-master/category-show', [LeaveMasterController::class, 'leaveCategoryShow'])->name('leave-master.category-show');
        Route::put('leave-master/category-update/{id}', [LeaveMasterController::class, 'leaveCategoryUpdate'])->name('leave-master.category-update');
        Route::delete('leave-master/category-delete/{id}', [LeaveMasterController::class, 'leaveCategoryDelete'])->name('leave-master.category-delete');
        Route::put("leave-master/category-restore/{id}", [LeaveMasterController::class, 'leaveCategoryRestore'])->name('leave-master.category-restore');
        
        Route::resource('leave', LeaveController::class);
        Route::resource('leave-master', LeaveMasterController::class);

        // Semester
        Route::group(['prefix' => 'semester'], static function () {
            Route::put('restore/{id}', [SemesterController::class, 'restore'])->name('semester.restore');
            Route::delete('trash/{id}', [SemesterController::class, 'trash'])->name('semester.trash');
        });
        Route::resource('semester', SemesterController::class);

        Route::group(['prefix' => 'stream'], static function () {
            Route::put('restore/{id}', [StreamController::class, 'restore'])->name('stream.restore');
            Route::delete('trash/{id}', [StreamController::class, 'trash'])->name('stream.trash');
        });
        Route::resource('stream', StreamController::class);


        Route::group(['prefix' => 'shift'], static function () {
            Route::put('restore/{id}', [ShiftController::class, 'restore'])->name('shift.restore');
            Route::delete('trash/{id}', [ShiftController::class, 'trash'])->name('shift.trash');
        });
        Route::resource('shift', ShiftController::class);

        Route::resource('faqs', FaqController::class);

        Route::get('users/status', [UserController::class, 'status']);
        Route::get('users/show', [UserController::class, 'show'])->name('users.show');
        Route::post('users/status', [UserController::class, 'status_change']);
        Route::get('users/birthday/{type?}', [UserController::class, 'birthday']);

        Route::group(['prefix' => 'related-data'], static function () {
            Route::get('/{table}/{id}', [Controller::class, 'relatedDataIndex'])->name('related-data.index');
            Route::delete('delete/{table}/{id}', [Controller::class, 'relatedDataDestroy'])->name('related-data.trash');
        });

        Route::resource('guidances', GuidanceController::class);
        Route::group(['prefix' => 'gallery'], static function () {
            Route::delete('file/delete/{id}', [GalleryController::class, 'deleteFile'])->name('gallery.delete');
        });
        Route::resource('gallery', GalleryController::class);
        Route::resource('notifications', NotificationController::class);

        Route::group(['prefix' => 'web-settings'], static function () {
            Route::get('feature-section', [WebSettingsController::class, 'feature_section_index'])->name('web-settings.feature.sections');
            Route::post('feature-section', [WebSettingsController::class, 'feature_section_store'])->name('web-settings.feature.sections.store');
            Route::get('section/show', [WebSettingsController::class, 'web_settings_show'])->name('web-settings-section.show');
            Route::get('section/{id}/edit', [WebSettingsController::class, 'web_settings_edit'])->name('web-settings-section.edit');
            Route::put('section/update/{id}', [WebSettingsController::class, 'web_settings_update'])->name('web-settings-section.update');
            Route::delete('section/delete/{id}', [WebSettingsController::class, 'feature_section_delete'])->name('web-settings-section.destroy');

            Route::get('video-settings-index', [WebSettingsController::class, 'videoSettingsIndex'])->name("video-settings.index");
            Route::post('video-settings-store', [WebSettingsController::class, 'storeVideoSetting'])->name("video-settings.store");
            Route::get('video-settings-show', [WebSettingsController::class, 'showVideoSettings'])->name('video-settings.show');
            Route::get('video-settings-edit/{id}', [WebSettingsController::class, 'editVideoSettings'])->name('video-settings.edit');
            Route::put('video-settings-update/{id}', [WebSettingsController::class, 'updateVideoSettings'])->name('video-settings.update');
            Route::delete('video-settings-destroy/{id}', [WebSettingsController::class, 'destroyVideoSettings'])->name('video-settings.destroy');
            Route::PATCH('feature-section/change/rank', [WebSettingsController::class, 'feature_section_rank'])->name('feature_section_rank');

            Route::get('picture-settings-index', [WebSettingsController::class, 'pictureSettingsIndex'])->name("picture-settings.index");
            Route::post('picture-settings-store', [WebSettingsController::class, 'storepictureSetting'])->name("picture-settings.store");
            Route::get('picture-settings-show', [WebSettingsController::class, 'showpictureSettings'])->name('picture-settings.show');
            Route::get('picture-settings-edit/{id}', [WebSettingsController::class, 'editpictureSettings'])->name('picture-settings.edit');
            Route::put('picture-settings-update/{id}', [WebSettingsController::class, 'updatepictureSettings'])->name('picture-settings.update');
            Route::delete('picture-settings-destroy/{id}', [WebSettingsController::class, 'destroyPictureSettings'])->name('picture-settings.destroy');
            Route::PATCH('feature-section/change/rank', [WebSettingsController::class, 'feature_section_rank'])->name('feature_section_rank');
        });

        Route::group(['prefix' => 'school'], static function () {
            Route::group(['prefix' => 'web-settings'], static function () {
                Route::get('/', [WebSettingsController::class, 'school_index'])->name('school.web-settings.index');
                Route::post('/', [WebSettingsController::class, 'school_store'])->name('school.web-settings.store');
            });
        });

        Route::resource('web-settings', WebSettingsController::class);

        // Certificates
        Route::group(['prefix' => 'certificate-template'], static function () {
            Route::get('design/{id}', [CertificateTemplateController::class, 'design'])->name('certificate-template.design');
            Route::put('design/{id}', [CertificateTemplateController::class, 'design_store'])->name('certificate-template.design.store');
        });

        Route::group(['prefix' => 'certificate'], static function () {
            Route::get('/', [CertificateTemplateController::class, 'certificate']);
            Route::post('/', [CertificateTemplateController::class, 'certificate_generate']);
            Route::post('send-bulk-emails', [CertificateTemplateController::class, 'sendBulkEmails'])->name('certificate.send-bulk-emails');
            Route::get('certificate/email/{id}', [CertificateTemplateController::class, 'certificate_email'])->name('certificate.email');
            Route::get('staff-certificate', [CertificateTemplateController::class, 'staff_certificate']);
            Route::post('staff-certificate', [CertificateTemplateController::class, 'staff_generate_certificate']);
        });
        Route::resource('certificate-template', CertificateTemplateController::class);
        Route::resource('class-group', ClassGroupController::class);

        Route::group(['prefix' => 'payroll-setting'], static function () {
            Route::put('restore/{id}', [PayrollSettingController::class, 'restore'])->name('payroll-setting.restore');
            Route::delete('trash/{id}', [PayrollSettingController::class, 'trash'])->name('payroll-setting.trash');
        });

        Route::resource('payroll-setting', PayrollSettingController::class);
    });
});

//booking
Route::group(['prefix' => 'booking'],static function(){
    // 获取学生列表的路由
    Route::get('/get-students-by-subject', [BookingController::class, 'getStudentsBySubject'])->name('booking.get-students-by-subject');
    // Route::get('getstudent',[BookingController::class,'getClassStudent'])->name('booking.getstudent');
    Route::get('show',[BookingController::class,'show'])->name('booking.show');
    Route::get('getsubject',[BookingController::class,'getSubjectByClass'])->name('booking.getsubject');
    Route::get('getteacher',[BookingController::class,'getTeacherBySubject'])->name('booking.getteacher');
    Route::get('getstudent/{id}',[BookingController::class,'getStudent'])->name('booking.getstudent');
    Route::get('getfilterstudent',[BookingController::class,'getFilterStudent'])->name('booking.filterstudent');
    Route::post('/booking/verify/{id}/{status}',[BookingController::class,'verifySlot'])->name('booking.verifyslot');
    Route::post('booked',[BookingController::class,'studentBook'])->name('booking.studentbook');
    Route::delete('delete/{id}',[BookingController::class,'trash'])->name('booking.delete');
    Route::put('restore/{id}',[BookingController::class,'restore'])->name('booking.restore');
    Route::get('/calendar', [BookingController::class, 'calendar'])->name('calendar');
    Route::get('/get-bookings', [BookingController::class, 'getBookings']);
    Route::get('/get-subjects-by-teacher/{teacherId}', [BookingController::class, 'getSubjectsByTeacher'])->name('get.subjects.by.teacher');
    Route::get('/get-students-by-teacher-subject', [BookingController::class, 'getStudentBySubject'])->name('get.student.by.subject');
        Route::get('/get-students-by-booking-slot/{id}', [BookingController::class, 'getStudentsByBookingSlot'])->name('booking.get-students-by-booking-slot');
});
Route::resource('booking',BookingController::class);

Route::group(['prefix' => 'bookingsetting'],static function(){
    Route::get('show',[BookingSettingController::class,'show'])->name('bookingsetting.show');
    Route::post('/booking-category/update/{id}', [BookingSettingController::class, 'update'])->name('bookingsetting.update');
    Route::delete('/booking-category/destroy/{id}', [BookingSettingController::class, 'destroy'])->name('bookingsetting.destroy');
    Route::put('/booking-category/restore/{id}', [BookingSettingController::class, 'restore'])->name('bookingsetting.restore');
    Route::delete('/booking-category/trash/{id}', [BookingSettingController::class, 'trash'])->name('bookingsetting.trash');
});


Route::resource('bookingsetting',BookingSettingController::class);


Route::get('privacy-policy', [Controller::class, 'privacy'])->name('privacy_policy.privacy');
Route::get('terms-conditions', [Controller::class, 'terms'])->name('terms-conditions');

// webhooks
Route::post('webhook/razorpay', [WebhookController::class, 'razorpay']);
Route::post('webhook/stripe', [WebhookController::class, 'stripe']);
Route::post('webhook/fiuu', [WebhookController::class, 'fiuu']);

Route::post('subscription/webhook/stripe', [SubscriptionWebhookController::class, 'stripe']);
Route::post('subscription/webhook/razorpay', [SubscriptionWebhookController::class, 'razorpay']);

// School terms & conditions
Route::get('school-settings/{id}/terms-condition', [SchoolSettingsController::class, 'public_terms_condition']);
Route::get('school-settings/{id}/privacy-policy', [SchoolSettingsController::class, 'public_privacy_policy']);
Route::get('school-settings/{id}/refund-cancellation', [SchoolSettingsController::class, 'public_refund_cancellation']);
// End school terms & conditions

//Self-Billing
Route::group(['prefix' => 'self-billing'],static function(){
    Route::group(['prefix' => 'supplier'],static function(){
        Route::delete("/{id}/deleted", [SupplierController::class, 'trash'])->name('supplier.trash');
        Route::put("/{id}/restore", [SupplierController::class, 'restore'])->name('supplier.restore');
        Route::get('validate-einvoice/{id}', [SupplierController::class, 'validateSupplierEInvoice'])->name('supplier.validate-einvoice');
    });
    Route::resource('supplier',SupplierController::class);
    Route::delete("/trash", [SelfBillingController::class, 'trash'])->name('self-billing.trash');
    Route::put("/restore", [SelfBillingController::class, 'restore'])->name('self-billing.restore');
    Route::delete("/{id}/delete/line-item", [SelfBillingController::class, 'deleteLineItem'])->name('self-billing.delete-line-item');
    Route::delete("/{id}/delete/tax-item", [SelfBillingController::class, 'deleteTaxItem'])->name('self-billing.delete-tax-item');
    Route::get('validate-einvoice/{id}', [SelfBillingController::class, 'validateShippingEInvoice'])->name('self-billing.shipping-validate');
    Route::post('e-invoice', [SelfBillingController::class, 'submitEInvoice'])->name('self-billing.e-invoice');
    Route::put('cancel-einvoice/{id}', [SelfBillingController::class,'cancelEInvoice'])->name('self-billing.cancel-einvoice');
    Route::get('{id}/{type}', [SelfBillingController::class, 'showInvoice'])->name('self-billing.invoice');
});
Route::resource('self-billing',SelfBillingController::class);

// Super admin
Route::get('page/privacy-policy', static function () {
    $cache = app(CachingService::class);
    echo htmlspecialchars_decode($cache->getSystemSettings('privacy_policy'));
})->name('public.privacy-policy');

Route::get('page/terms-conditions', static function () {
    $cache = app(CachingService::class);
    echo htmlspecialchars_decode($cache->getSystemSettings('terms_condition'));
})->name('public.terms-conditions');

Route::get('page/refund-cancellation', static function () {
    $cache = app(CachingService::class);
    echo htmlspecialchars_decode($cache->getSystemSettings('refund_cancellation'));
})->name('public.refund-cancellation');

Route::get('school-terms-condition', static function () {
    $cache = app(CachingService::class);
    echo htmlspecialchars_decode($cache->getSystemSettings('school_terms_condition'));
});

Route::get('clear', static function () {
    Artisan::call('view:clear');
    Artisan::call('route:clear');
    Artisan::call('config:clear');
    Artisan::call('cache:clear');
    Artisan::call('optimize:clear');
    return redirect()->back();
});

Route::get('storage-link', static function () {
    try {
        Artisan::call('storage:link');
        echo "storage link created";
    } catch (Exception) {
        echo "Storage Link already exists";
    }
    return redirect()->back();
});


Route::get('migrate', static function () {
    Artisan::call('migrate');
    echo "Done";
    return false;
});

Route::get('start-websocket', static function () {
    Artisan::call('websocket:init');
   return redirect()->back();
    return false;
});

Route::get('migrate-rollback', static function () {
    Artisan::call('migrate:rollback');
    echo "Done";
    return false;
});
Route::get('installation-seeder', static function () {
    Artisan::call('db:seed --class=InstallationSeeder');
    echo "Done";
    return false;
});

Route::get('dummy-seeder', static function () {
    Artisan::call('db:seed --class=DummyDataSeeder');
    // Artisan::call('db:seed');
    return redirect()->back();
    return false;
});

//Route::get('test', static function () {
//    // Replace 'A' with the table you are interested in
//    $table = 'mediums';
//    $id = 1;
//    $databaseName = config('database.connections.mysql.database');
//
//    $relatedTables = DB::select("SELECT TABLE_NAME,COLUMN_NAME
//            FROM information_schema.KEY_COLUMN_USAGE
//            WHERE REFERENCED_TABLE_NAME = ? AND TABLE_SCHEMA = ?", [$table, $databaseName]);
//    $data = [];
//
//    //    dd($relatedTables);
//
//    foreach ($relatedTables as $relatedTable) {
//
//        $getTableSchema = DB::select("SELECT CONSTRAINT_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
//            FROM information_schema.KEY_COLUMN_USAGE
//            WHERE TABLE_NAME = ? AND TABLE_SCHEMA = ? AND REFERENCED_TABLE_NAME IS NOT NULL", [$relatedTable->TABLE_NAME, $databaseName]);
//
//        //        dd($getTableSchema);
//        DB::enableQueryLog();
//        $q = DB::table($relatedTable->TABLE_NAME)->where($relatedTable->TABLE_NAME . "." . $relatedTable->COLUMN_NAME, $id);
//
//        //Build Join query for all the foreign key using the Table Schema
//        foreach ($getTableSchema as $foreignKey) {
//            if ($foreignKey->REFERENCED_TABLE_NAME != 'schools') {
//                $q->join($foreignKey->REFERENCED_TABLE_NAME, $foreignKey->REFERENCED_TABLE_NAME . "." . $foreignKey->REFERENCED_COLUMN_NAME, '=', $relatedTable->TABLE_NAME . "." . $foreignKey->COLUMN_NAME);
//            }
//        }
//
//        //        $q = $this->buildQueryForSpecificTable($q, $relatedTable->TABLE_NAME);
//
//        $data[$relatedTable->TABLE_NAME] = $q->select('*')->get()->toArray();
//        print_r($data[$relatedTable->TABLE_NAME]);
//        //        dd(DB::getQueryLog());
//        //            $data[$relatedTable->TABLE_NAME] = DB::table($relatedTable->TABLE_NAME)->where($relatedTable->COLUMN_NAME, $id)->get()->toArray();
//    }
//
//    //    dd($data);
//    //
//    //    $data = [];
//    //
//    //    dd($referencingTables);
//    //    foreach ($referencingTables as $table) {
//    //        $data[$table->REFERENCED_TABLE_NAME] = DB::table($table->TABLE_NAME)->where($table->REFERENCED_COLUMN_NAME, $id)->get()->toArray();
//    //    }
//
//    // Now $referencingTables contains an array of tables that reference 'A'
//});

Route::get('/js/lang', static function () {
    //    https://medium.com/@serhii.matrunchyk/using-laravel-localization-with-javascript-and-vuejs-23064d0c210e
    header('Content-Type: text/javascript');
    $labels = \Illuminate\Support\Facades\Cache::remember('lang.js', 3600, static function () {
        $lang = app()->getLocale();
        $files = resource_path('lang/' . $lang . '.json');
        return File::get($files);
    });
    echo('window.trans = ' . $labels);
    exit();
})->name('assets.lang');

Route::get('test-code', static function () {});

Route::get('cache-flush', static function () {
    \Illuminate\Support\Facades\Cache::flush();
    Session::put('landing_locale', null);
    Session::save();
    return redirect()->back();
});


Route::get('demo-tokens', static function () {
    echo "<pre>";

    $guardian = User::where('email', '<EMAIL>')->first();
    if (!empty($guardian)) {
        echo "Demo Guardian Token<br>";
        echo Cache::rememberForever('demoGuardianToken', static function () use ($guardian) {
            return $guardian->createToken($guardian->first_name)->plainTextToken;
        });
    }


    $student = User::where('email', '<EMAIL>')->first();
    if (!empty($student)) {
        echo "<br><br>Demo Student Token<br>";
        echo Cache::rememberForever('demoStudentToken', static function () use ($student) {
            return $student->createToken($student->first_name)->plainTextToken;
        });
    }
});
